import 'dart:async';
import 'dart:io';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:tmt_mobile/models/remote_work_request_model.dart';
import 'package:tmt_mobile/models/user_mood_model.dart';
import 'package:tmt_mobile/models/userdata.dart';
import 'package:tmt_mobile/models/usertag.dart';
import 'package:tmt_mobile/models/vacation_request_model.dart';
import 'package:tmt_mobile/utils/userPrefrences.dart';
import 'dart:convert';

import 'package:tmt_mobile/utils/utils.dart';

class UserServices {
  Future<http.Response> login(userLogin data) async {
    try {
      // Make the HTTP POST request
      final response = await http.post(
        Uri.parse('${apiUrl}api/login'),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: jsonEncode({
          'email': data.email,
          'password': data.password,
        }),
      );

      // Log the response and handle different status codes
      if (response.statusCode >= 200 && response.statusCode < 300) {
        print('Login successful: ${response.body}');
      } else {
        print('Login failed: ${response.statusCode} - ${response.body}');
      }

      return response;
    } catch (e) {
      // Handle different types of exceptions
      if (e is SocketException) {
        print('Network Error: No internet connection. Details: $e');
        return http.Response('{"error": "No internet connection"}', 503);
      } else if (e is HttpException) {
        print('HTTP Error: Server issue. Details: $e');
        return http.Response('{"error": "HTTP exception occurred"}', 500);
      } else if (e is FormatException) {
        print('Data Error: Invalid JSON format. Details: $e');
        return http.Response('{"error": "Invalid JSON format"}', 400);
      } else {
        // Handle unexpected errors
        print('Unexpected Error: Something went wrong. Details: $e');
        return http.Response('{"error": "Unexpected error occurred"}', 500);
      }
    }
  }

  Future<http.Response> sendValidationCode(String email) {
    return http.post(
      Uri.parse('${apiUrl}SendEmailValidation?email=$email'),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
      },
    );
  }

  Future<http.Response> sendResetCode(String email) {
    return http.post(
      Uri.parse('${apiUrl}SendEmailReset?email=$email'),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
      },
    );
  }

  Future<http.Response> registration(userRegistration data) async {
    try {
      // Attempt to make the HTTP POST request
      final response = await http.post(
        Uri.parse('${apiUrl}register'),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: jsonEncode({
          'email': data.email,
          'firstname': data.firstname,
          'password': data.password,
          'lastname': data.lastname,
        }),
      );

      // Check for success
      if (response.statusCode >= 200 && response.statusCode < 300) {
        print('Success: Registration successful!');
        print('Response: ${response.body}');
      } else {
        print(
            'Error: Registration failed with status code ${response.statusCode}');
        print('Response body: ${response.body}');
      }

      return response;
    } catch (e) {
      // Handle different error types
      if (e is SocketException) {
        print('Network Error: No internet connection. Details: $e');
        return http.Response('{"error": "No internet connection"}', 503);
      } else if (e is HttpException) {
        print('HTTP Error: Server issue. Details: $e');
        return http.Response('{"error": "HTTP exception occurred"}', 500);
      } else if (e is FormatException) {
        print('Data Error: Invalid JSON format. Details: $e');
        return http.Response('{"error": "Invalid JSON format"}', 400);
      } else {
        // Generic error logging
        print('Unexpected Error: Something went wrong. Details: $e');
        return http.Response('{"error": "Unexpected error occurred"}', 500);
      }
    }
  }

  Future<http.Response> addTimesheet(
      int idPj, int idPl, int idPt, DateTime tsDay, double value) async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    var body = jsonEncode({
      'IdProjet': idPj,
      'IdProjetLot': idPl,
      'IdProjetTask': idPt,
      'TsDay': tsDay.toIso8601String(),
      'Value': value
    });
    return http.post(
      Uri.parse('${apiUrl}AddTimeSheets'),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        "accept": "application/json",
        'Authorization': 'Bearer $token',
      },
      body: body,
    );
  }

  Future<http.Response> deleteTimesheet(int id) async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    return http.delete(Uri.parse('${apiUrl}deleteTimeSheets?id=$id'), headers: {
      "Accept": "application/json",
      'Authorization': 'Bearer $token'
    });
  }

  Future<http.Response> getAllOrgs(String email) async {
    try {
      final storage = FlutterSecureStorage();
      var token = await storage.read(key: "jwt");

      if (token == null || token.isEmpty) {
        print("No JWT token found for getAllOrgs");
        return http.Response('{"error": "No authentication token"}', 401);
      }

      print("Making getAllOrgs request for email: $email");
      var response = await http
          .get(Uri.parse('${apiUrl}getalluserorg?email=$email'), headers: {
        "Accept": "application/json",
        'Authorization': 'Bearer $token',
      }).timeout(Duration(seconds: 30));

      print("getAllOrgs response: ${response.statusCode} - ${response.body}");
      return response;
    } catch (e) {
      print("Exception in getAllOrgs: $e");
      return http.Response('{"error": "Network error: $e"}', 0);
    }
  }

  Future<http.Response> getOneOrg(String guid) async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    return http.get(Uri.parse('${apiUrl}getOneOrg?guid=$guid'), headers: {
      "Accept": "application/json",
      'Authorization': 'Bearer $token',
    });
  }

  Future<http.Response> getProjects() async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    return http.get(Uri.parse('${apiUrl}getProjects'), headers: {
      "Accept": "application/json",
      'Authorization': 'Bearer $token',
    });
  }

  Future<http.Response> getRecentTimeSheets() async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    return http.get(Uri.parse('${apiUrl}getRecentTimeSheets'), headers: {
      "Accept": "application/json",
      'Authorization': 'Bearer $token',
    });
  }

  Future<http.Response> copyLastTimeSheet() async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    return http.get(Uri.parse('${apiUrl}copyLastTimeSheet'), headers: {
      "Accept": "application/json",
      'Authorization': 'Bearer $token',
    });
  }

  Future<http.Response> getProjectslots(int idprojet) async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    return http.get(Uri.parse('${apiUrl}getProjectlots?projectId=$idprojet'),
        headers: {
          "Accept": "application/json",
          'Authorization': 'Bearer $token',
        });
  }

  Future<http.Response> getProjectsTasks(int projectLot) async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");

    // Ensure token is not null before making a request
    if (token == null) {
      // Handle missing token scenario, perhaps notify the user
      return Future.error('User not authenticated.');
    }

    return http.get(
      Uri.parse('${apiUrl}getProjectTasks?projectlotid=$projectLot'),
      headers: {
        "Accept": "application/json",
        'Authorization': 'Bearer $token',
      },
    );
  }

  Future<int> connected() async {
    var response = await isAuthentifed();
    var validAccount = UserPrefrences.getCureentIsValid();
    var userEmail = UserPrefrences.getUserEmail();
    var org = UserPrefrences.getCureentOrg();

    print(response.body);
    print(userEmail);

    // Check the validity of the connection
    if (response.statusCode == 200 &&
        validAccount == true &&
        userEmail!.isNotEmpty) {
      return (org != null && org.isNotEmpty) ? 1 : 2;
    }

    // Return 0 if not connected
    return 0;
  }

  Future<http.Response> isAuthentifed() async {
    try {
      final storage = FlutterSecureStorage();
      var token = await storage.read(key: "jwt");
      print(token);

      var response =
          await http.get(Uri.parse('${apiUrl}authentified'), headers: {
        "Accept": "application/json",
        'Authorization': 'Bearer $token',
      }).timeout(Duration(seconds: 30));

      return response;
    } catch (e) {
      print('Error in isAuthentifed: $e');
      // Return a proper error response instead of throwing
      if (e is SocketException) {
        return http.Response('{"error": "No internet connection"}', 503);
      } else if (e is TimeoutException) {
        return http.Response('{"error": "Connection timeout"}', 408);
      } else {
        return http.Response('{"error": "Network error: $e"}', 500);
      }
    }
  }

  Future<http.Response> getTimesheet(DateTime date) async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    return http.get(Uri.parse('${apiUrl}getTimeSheets?date=$date'), headers: {
      "Accept": "application/json",
      'Authorization': 'Bearer $token',
    });
  }

  Future<http.Response> submitUserMood(UserMood mood) async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    return http.post(
      Uri.parse('${apiUrl}api/UserMood'),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode(mood.toJson()),
    );
  }

  Future<http.Response> getVacationRequests() async {
    try {
      final storage = FlutterSecureStorage();
      var token = await storage.read(key: "jwt");

      if (token == null || token.isEmpty) {
        print("UserServices: No JWT token found for getVacationRequests");
        return http.Response('{"error": "No authentication token"}', 401);
      }

      print("UserServices: Making getVacationRequests API call...");
      print("UserServices: GET ${apiUrl}LeaveRequest/my-leave-requests");

      var response = await http
          .get(Uri.parse('${apiUrl}LeaveRequest/my-leave-requests'), headers: {
        "Accept": "application/json",
        'Authorization': 'Bearer $token',
      }).timeout(Duration(seconds: 30));

      print(
          "UserServices: getVacationRequests response: ${response.statusCode} - ${response.body}");
      return response;
    } catch (e) {
      print("UserServices: Exception in getVacationRequests: $e");
      return http.Response('{"error": "Network error: $e"}', 0);
    }
  }

  Future<http.Response> createVacationRequest(
      Map<String, dynamic> requestDto) async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    return http.post(
      Uri.parse('${apiUrl}LeaveRequest'),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode(requestDto),
    );
  }

  Future<http.Response> changepassword(
      String email, String code, String password) {
    final uri = Uri.parse(
      '${apiUrl}ResetPassowrd?email=$email&resetCode=$code&password=$password',
    );
    return http.post(
      uri,
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
      },
    );
  }

  Future<http.Response> getLeaveRequestTypes() async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    return http.get(Uri.parse('${apiUrl}api/AppVariable/leave-request-types'),
        headers: {
          "Accept": "application/json",
          'Authorization': 'Bearer $token',
        });
  }

  Future<http.Response> getManagers() async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    return http.get(Uri.parse('${apiUrl}api/AppUser/get-my-manager'), headers: {
      "Accept": "application/json",
      'Authorization': 'Bearer $token',
    });
  }

  Future<http.Response> deleteVacationRequest(int requestId) async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    return http.delete(
      Uri.parse('${apiUrl}LeaveRequest/$requestId'),
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      },
    );
  }

  Future<http.Response> updateVacationRequest(
      Map<String, dynamic> requestDto) async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    print('Token: $token');
    print('Request URL: ${apiUrl}LeaveRequest/${requestDto['id']}');
    print('Request Body: ${jsonEncode(requestDto)}');

    var response = await http.put(
      Uri.parse('${apiUrl}LeaveRequest/${requestDto['id']}'),
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode(requestDto),
    );

    print('Response Status: ${response.statusCode}');
    print('Response Body: ${response.body}');
    return response;
  }

  Future<http.Response> confirmation(String email, String code) {
    final queryparams = {
      'email': email,
      'registrationCode': code,
    };
    final uri = Uri.parse(
      '${apiUrl}registryconfirmation?email=$email&registrationCode=$code',
    );
    return http.post(
      uri,
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
      },
    );
  }

  Future<http.Response> getValidationStatuses() async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    return http.get(Uri.parse('${apiUrl}api/AppVariable/validation-statuses'),
        headers: {
          "Accept": "application/json",
          'Authorization': 'Bearer $token',
        });
  }

  Future<http.Response> deleteRemoteWorkRequest(int requestId) async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    return http.delete(
      Uri.parse('${apiUrl}api/RemoteWorkRequest/$requestId'),
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      },
    );
  }

  Future<http.Response> updateRemoteWorkRequest(
      Map<String, dynamic> requestDto) async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    print('Token: $token');
    print('Request URL: ${apiUrl}RemoteWorkRequest/${requestDto['id']}');
    print('Request Body: ${jsonEncode(requestDto)}');

    var response = await http.put(
      Uri.parse('${apiUrl}api/RemoteWorkRequest/${requestDto['id']}'),
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode(requestDto),
    );

    print('Response Status: ${response.statusCode}');
    print('Response Body: ${response.body}');
    return response;
  }

  Future<http.Response> getRemoteWorkRequests() async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    final url = '${apiUrl}api/RemoteWorkRequest/my-remote-work';
    final headers = {
      "Accept": "application/json",
      'Authorization': 'Bearer $token',
    };

    try {
      print('GET $url');
      final response = await http.get(Uri.parse(url), headers: headers);
      print('Response Status Code: ${response.statusCode}');
      print('Response Body: ${response.body}');
      return response;
    } catch (e) {
      print('An error occurred while fetching remote work requests: $e');
      rethrow;
    }
  }

  Future<http.Response> createRemoteWorkRequest(
      Map<String, dynamic> requestDto) async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    return http.post(
      Uri.parse('${apiUrl}api/RemoteWorkRequest'),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode(requestDto),
    );
  }

  Future<http.Response> approveVacationRequest(
      int requestId, String comment) async {
    final storage = FlutterSecureStorage();
    final token = await storage.read(key: "jwt");

    return http.put(
      Uri.parse('${apiUrl}LeaveRequest/accept/manager/$requestId'),
      headers: {
        "Content-Type": "application/json",
        'Authorization': 'Bearer $token',
      },
      body: json.encode({
        "Managercomment": comment,
      }),
    );
  }

  Future<http.Response> rejectVacationRequest(
      int requestId, String comment) async {
    final storage = FlutterSecureStorage();
    final token = await storage.read(key: "jwt");

    return http.put(
      Uri.parse('${apiUrl}LeaveRequest/reject/manager/$requestId'),
      headers: {
        "Content-Type": "application/json",
        'Authorization': 'Bearer $token',
      },
      body: json.encode({
        "Managercomment": comment,
      }),
    );
  }

  Future<http.Response> approveRemoteWorkRequest(
      int requestId, String comment) async {
    final storage = FlutterSecureStorage();
    final token = await storage.read(key: "jwt");

    return http.put(
      Uri.parse('${apiUrl}api/RemoteWorkRequest/$requestId/accept'),
      headers: {
        'Content-Type': 'application/json',
        "Accept": "application/json",
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({"comment": comment}),
    );
  }

  Future<http.Response> rejectRemoteWorkRequest(
      int requestId, String comment) async {
    final storage = FlutterSecureStorage();
    final token = await storage.read(key: "jwt");

    return http.put(
      Uri.parse('${apiUrl}api/RemoteWorkRequest/$requestId/reject'),
      headers: {
        'Content-Type': 'application/json',
        "Accept": "application/json",
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({"comment": comment}),
    );
  }

  Future<http.Response> getUserMoods() async {
    final storage = FlutterSecureStorage();
    final token = await storage.read(key: "jwt");

    print('Making API call to fetch user moods...');
    return http.get(
      Uri.parse('${apiUrl}api/UserMood/my-user-mood'),
      headers: {
        "Accept": "application/json",
        'Authorization': 'Bearer $token',
      },
    );
  }

  static Future<http.Response> getUserTags() async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    return http.get(Uri.parse('${apiUrl}api/UserTags/my-user-tags'), headers: {
      "Accept": "application/json",
      'Authorization': 'Bearer $token',
    });
  }

  static Future<http.Response> addUserTag(UserTag userTag) async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    return http.post(
      Uri.parse('${apiUrl}api/UserTags'),
      headers: {
        "Content-Type": "application/json",
        'Authorization': 'Bearer $token',
      },
      body: json.encode(userTag.toJson()),
    );
  }

  static Future<http.Response> getTags() async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    return http.get(Uri.parse('${apiUrl}api/Tags'), headers: {
      "Accept": "application/json",
      'Authorization': 'Bearer $token',
    });
  }

  Future<http.Response> reassignManager(
      int requestId, int managerId, String comment) async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    var body = json.encode({
      'idassignedtomanager': managerId,
      'managercomment': comment,
    });
    print('Request Body: $body'); // Log the request body
    try {
      var response = await http.put(
        Uri.parse('${apiUrl}LeaveRequest/reassign-manager/$requestId'),
        headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $token',
        },
        body: body,
      );
      print(
          'Response Status: ${response.statusCode}'); // Log the response status
      print('Response Body: ${response.body}'); // Log the response body
      return response;
    } catch (e) {
      print('Error making reassign manager request: $e'); // Log any errors
      rethrow;
    }
  }

  Future<http.Response> reassignManagerR(
      int requestId, int assignedToManagerId, String comment) async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    var body = json.encode({
      'assignedToManagerId': assignedToManagerId,
      'comment': comment,
    });
    print('Request Body: $body'); // Log the request body
    try {
      var response = await http.put(
        Uri.parse('$apiUrl/api/RemoteWorkRequest/$requestId/reassign-manager'),
        headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $token',
        },
        body: body,
      );
      print(
          'Response Status: ${response.statusCode}'); // Log the response status
      print('Response Body: ${response.body}'); // Log the response body
      return response;
    } catch (e) {
      print('Error making reassign manager request: $e'); // Log any errors
      rethrow;
    }
  }

  Future<http.Response> getAssignedVacationRequests() async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    return http.get(
      Uri.parse('${apiUrl}LeaveRequest/assigned-to-me'),
      headers: {
        "Accept": "application/json",
        'Authorization': 'Bearer $token',
      },
    );
  }

  Future<http.Response> getAssignedRemoteWorkRequests() async {
    final storage = FlutterSecureStorage();
    var token = await storage.read(key: "jwt");
    return http.get(
      Uri.parse('${apiUrl}api/RemoteWorkRequest/assigned-to-me'),
      headers: {
        "Accept": "application/json",
        'Authorization': 'Bearer $token',
      },
    );
  }
}
