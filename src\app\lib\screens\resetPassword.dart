import 'package:email_validator/email_validator.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:tmt_mobile/controllers/ResetPasswordController.dart';
import 'package:tmt_mobile/controllers/globalcontroller.dart';

import 'package:tmt_mobile/screens/landingScreen.dart';

import 'package:tmt_mobile/widgets/buttonwithicon.dart';

import '../utils/myColors.dart';
import '../widgets/big_text.dart';
import '../widgets/inputfield.dart';

class ResetPasswordScreen extends GetView<ResetPasswordController> {
  const ResetPasswordScreen({super.key});

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;

    GlobalController globalController = Get.find<GlobalController>();
    Get.put(ResetPasswordController());

    return Scaffold(
        backgroundColor: Colors.grey[50],
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.grey[50]!,
                Colors.white,
                Colors.grey[50]!,
              ],
            ),
          ),
          child: globalController.devType.value == "tablet"
              ? restPasswordScreenTablet(
                  screenHeight, screenWidth, globalController)
              : resetPasswordScreenAndroid(
                  screenWidth, screenHeight, globalController),
        ));
  }

  Widget resetPasswordScreenAndroid(double screenWidth, double screenHeight,
      GlobalController globalController) {
    return SafeArea(
      child: SingleChildScrollView(
        physics: BouncingScrollPhysics(),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: screenWidth * .06),
          child: Obx(
            () => Form(
              key: controller.SignInKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(height: screenHeight * .08),

                  // Logo and Header Section
                  _buildHeaderSection(
                      screenWidth, screenHeight, globalController),

                  SizedBox(height: screenHeight * 0.05),

                  // Main Form Card
                  _buildFormCard(screenWidth, screenHeight, globalController),

                  SizedBox(height: screenHeight * 0.04),

                  // Footer Section
                  _buildFooterSection(
                      screenWidth, screenHeight, globalController),

                  SizedBox(height: screenHeight * 0.03),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderSection(double screenWidth, double screenHeight,
      GlobalController globalController) {
    return Column(
      children: [
        // Logo with animation
        Container(
          padding: EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: MyColors.MainRedBig.withOpacity(0.1),
                spreadRadius: 5,
                blurRadius: 15,
                offset: Offset(0, 5),
              ),
            ],
          ),
          child: Icon(
            Icons.lock_reset,
            size: screenWidth * 0.12,
            color: MyColors.MainRedBig,
          ),
        ),

        SizedBox(height: screenHeight * 0.03),

        // Title
        BigText(
          text: globalController.lang.value == "fr"
              ? "Réinitialiser mot de passe"
              : "Reset Password",
          color: MyColors.mainblack,
          textAlign: TextAlign.center,
          size: screenHeight * 0.032,
        ),

        SizedBox(height: screenHeight * 0.015),

        // Subtitle
        Text(
          globalController.lang.value == "fr"
              ? "Entrez votre email pour recevoir un code de réinitialisation"
              : "Enter your email to receive a reset code",
          textAlign: TextAlign.center,
          style: TextStyle(
            color: MyColors.BordersGrey,
            fontSize: screenHeight * 0.018,
            fontFamily: "Roboto",
          ),
        ),
      ],
    );
  }

  Widget _buildFormCard(double screenWidth, double screenHeight,
      GlobalController globalController) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(screenWidth * 0.06),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            spreadRadius: 0,
            blurRadius: 20,
            offset: Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          // Email Input (Step 1)
          Visibility(
            visible: controller.EmailSent.value == false,
            child: Column(
              children: [
                _buildStepIndicator(1, 2, globalController),
                SizedBox(height: screenHeight * 0.03),
                Myinput(
                  labelText: globalController.lang.value == "fr"
                      ? "Adresse email"
                      : "Email address",
                  controller: controller.email.value,
                  icon: Icons.email_outlined,
                  validate: (v) => v != null && !EmailValidator.validate(v)
                      ? (globalController.lang.value == "fr"
                          ? 'Entrez un email valide'
                          : 'Enter a valid email')
                      : null,
                ),
              ],
            ),
          ),

          // Code and Password Inputs (Step 2)
          Visibility(
            visible: controller.EmailSent.value == true,
            child: Column(
              children: [
                _buildStepIndicator(2, 2, globalController),
                SizedBox(height: screenHeight * 0.03),
                Myinput(
                  labelText: globalController.lang.value == "fr"
                      ? "Code de validation"
                      : "Validation code",
                  controller: controller.Codevalidation.value,
                  validate: (v) => v == ""
                      ? (globalController.lang.value == "fr"
                          ? 'Code de validation requis'
                          : 'Validation code required')
                      : null,
                  icon: Icons.security,
                ),
                SizedBox(height: screenHeight * 0.025),
                Myinput(
                  labelText: globalController.lang.value == "fr"
                      ? "Nouveau mot de passe"
                      : "New password",
                  controller: controller.password.value,
                  validate: (v) => controller.validatePasswordlen(v!),
                  obscureText: controller.passwtoggl.value,
                  icon: Icons.lock_outline,
                  Suffixicon: Icons.visibility_outlined,
                  Suffixiconoff: Icons.visibility_off_outlined,
                  suffixiconfun: () {
                    controller.passwtoggl.value = !controller.passwtoggl.value;
                  },
                ),
                SizedBox(height: screenHeight * 0.025),
                Myinput(
                  obscureText: controller.passwtogg2.value,
                  labelText: globalController.lang.value == "fr"
                      ? "Confirmer le mot de passe"
                      : "Confirm password",
                  controller: controller.confirmpaswword.value,
                  validate: (v) => controller.validatePassword(
                      controller.password.value.text, v!),
                  icon: Icons.lock_outline,
                  Suffixicon: Icons.visibility_outlined,
                  Suffixiconoff: Icons.visibility_off_outlined,
                  suffixiconfun: () {
                    controller.passwtogg2.value = !controller.passwtogg2.value;
                  },
                ),
              ],
            ),
          ),

          SizedBox(height: screenHeight * 0.03),

          // Error Message
          Visibility(
            visible: controller.showError.value,
            child: Container(
              padding: EdgeInsets.all(12),
              margin: EdgeInsets.only(bottom: screenHeight * 0.02),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: Colors.red.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.red, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      controller.errormsg.value,
                      style: TextStyle(
                        color: Colors.red,
                        fontSize: 14,
                        fontFamily: "Roboto",
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Action Button
          controller.loading.value == false
              ? _buildActionButton(screenWidth, screenHeight, globalController)
              : Container(
                  height: screenHeight * 0.065,
                  child: Center(
                    child: CircularProgressIndicator(
                      color: MyColors.MainRedBig,
                      strokeWidth: 3,
                    ),
                  ),
                ),

          // Resend Code Button
          Visibility(
            visible: controller.EmailSent.value == true,
            child: Padding(
              padding: EdgeInsets.only(top: screenHeight * 0.02),
              child: TextButton(
                onPressed: () async {
                  if (controller.loading.value == false) {
                    await controller
                        .sendResetPassword(controller.email.value.text);
                  }
                },
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                ),
                child: Text(
                  globalController.lang.value == "fr"
                      ? "Renvoyer le code"
                      : "Resend code",
                  style: TextStyle(
                    fontSize: 16,
                    fontFamily: "Roboto",
                    color: MyColors.thirdColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(
      int currentStep, int totalSteps, GlobalController globalController) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        for (int i = 1; i <= totalSteps; i++)
          Row(
            children: [
              Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color:
                      i <= currentStep ? MyColors.MainRedBig : Colors.grey[300],
                  border: Border.all(
                    color: i <= currentStep
                        ? MyColors.MainRedBig
                        : Colors.grey[400]!,
                    width: 2,
                  ),
                ),
                child: Center(
                  child: i < currentStep
                      ? Icon(Icons.check, color: Colors.white, size: 16)
                      : Text(
                          '$i',
                          style: TextStyle(
                            color: i <= currentStep
                                ? Colors.white
                                : Colors.grey[600],
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                ),
              ),
              if (i < totalSteps)
                Container(
                  width: 40,
                  height: 2,
                  color:
                      i < currentStep ? MyColors.MainRedBig : Colors.grey[300],
                ),
            ],
          ),
      ],
    );
  }

  Widget _buildActionButton(double screenWidth, double screenHeight,
      GlobalController globalController) {
    return Container(
      width: double.infinity,
      height: screenHeight * 0.065,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [MyColors.MainRedBig, MyColors.MainRedSecond],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: MyColors.MainRedBig.withOpacity(0.3),
            spreadRadius: 0,
            blurRadius: 10,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(15),
          onTap: () async {
            bool valid = controller.SignInKey.currentState!.validate();
            if (valid) {
              if (controller.EmailSent.value == false) {
                await controller.sendResetPassword(controller.email.value.text);
              } else {
                await controller.changePassword(
                  controller.email.value.text,
                  controller.Codevalidation.value.text,
                  controller.password.value.text,
                );
              }
            }
          },
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  controller.EmailSent.value == false
                      ? Icons.send
                      : Icons.lock_reset,
                  color: Colors.white,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  controller.EmailSent.value == false
                      ? (globalController.lang.value == "fr"
                          ? "Envoyer le code"
                          : "Send code")
                      : (globalController.lang.value == "fr"
                          ? "Changer le mot de passe"
                          : "Change password"),
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontFamily: "Roboto",
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFooterSection(double screenWidth, double screenHeight,
      GlobalController globalController) {
    return Column(
      children: [
        Row(
          children: <Widget>[
            Expanded(
              child: Divider(
                color: MyColors.Strokecolor.withOpacity(0.5),
                height: 1,
                thickness: 1,
                indent: 20,
                endIndent: 10,
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 15),
              child: Text(
                globalController.lang.value == "fr" ? "ou" : "or",
                style: TextStyle(
                  fontSize: 14,
                  color: MyColors.BordersGrey,
                  fontFamily: "Roboto",
                ),
              ),
            ),
            Expanded(
              child: Divider(
                color: MyColors.Strokecolor.withOpacity(0.5),
                height: 1,
                thickness: 1,
                indent: 10,
                endIndent: 20,
              ),
            ),
          ],
        ),
        SizedBox(height: screenHeight * 0.025),
        RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            style: TextStyle(
              color: MyColors.BordersGrey,
              fontSize: 15,
              fontFamily: "Roboto",
            ),
            children: [
              TextSpan(
                text: globalController.lang.value == "fr"
                    ? "Vous n'avez pas de compte? "
                    : "Don't have an account? ",
              ),
              TextSpan(
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    Get.offAll(LandingScreen());
                  },
                text: globalController.lang.value == "fr"
                    ? 'Inscrivez-vous'
                    : "Sign up",
                style: TextStyle(
                  color: MyColors.thirdColor,
                  fontWeight: FontWeight.w600,
                  decoration: TextDecoration.underline,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  SafeArea restPasswordScreenTablet(double screenHeight, double screenWidth,
      GlobalController globalController) {
    return SafeArea(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.grey[50]!,
              Colors.white,
              Colors.grey[100]!,
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(40),
          child: Center(
            child: Obx(() => SingleChildScrollView(
                  child: Form(
                    key: controller.SignInKey,
                    child: Container(
                      constraints: BoxConstraints(
                          maxWidth: 1000, maxHeight: screenHeight * 0.85),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            spreadRadius: 0,
                            blurRadius: 30,
                            offset: Offset(0, 15),
                          ),
                        ],
                      ),
                      child: Row(
                        children: [
                          // Left Side - Branding
                          Expanded(
                            flex: 5,
                            child: Container(
                              padding: EdgeInsets.all(40),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    MyColors.MainRedBig.withOpacity(0.1),
                                    MyColors.thirdColor.withOpacity(0.1),
                                  ],
                                ),
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(25),
                                  bottomLeft: Radius.circular(25),
                                ),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    padding: EdgeInsets.all(30),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color:
                                              MyColors.MainRedBig.withOpacity(
                                                  0.2),
                                          spreadRadius: 5,
                                          blurRadius: 20,
                                          offset: Offset(0, 10),
                                        ),
                                      ],
                                    ),
                                    child: SvgPicture.asset(
                                      "assets/logotmt.svg",
                                      width: screenWidth * 0.15,
                                      height: screenWidth * 0.15,
                                    ),
                                  ),
                                  SizedBox(height: 30),
                                  BigText(
                                    text: "TM/T Software",
                                    color: MyColors.mainblack,
                                    size: 32,
                                    textAlign: TextAlign.center,
                                  ),
                                  SizedBox(height: 15),
                                  Text(
                                    "PILOTEZ VOS PROJETS AVEC LES BONS INDICATEURS",
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: MyColors.BordersGrey,
                                      fontFamily: "Roboto",
                                      height: 1.4,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),

                          // Right Side - Form
                          Expanded(
                            flex: 6,
                            child: Padding(
                              padding: EdgeInsets.all(40),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Header
                                  _buildTabletHeader(globalController),

                                  SizedBox(height: 40),

                                  // Form Content
                                  _buildTabletFormContent(screenWidth,
                                      screenHeight, globalController),

                                  SizedBox(height: 30),

                                  // Footer
                                  _buildTabletFooter(globalController),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                )),
          ),
        ),
      ),
    );
  }

  Widget _buildTabletHeader(GlobalController globalController) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: MyColors.MainRedBig.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.lock_reset,
                color: MyColors.MainRedBig,
                size: 24,
              ),
            ),
            SizedBox(width: 15),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  BigText(
                    text: globalController.lang.value == "fr"
                        ? "Réinitialiser mot de passe"
                        : "Reset Password",
                    color: MyColors.mainblack,
                    size: 28,
                  ),
                  SizedBox(height: 5),
                  Text(
                    globalController.lang.value == "fr"
                        ? "Sécurisez votre compte avec un nouveau mot de passe"
                        : "Secure your account with a new password",
                    style: TextStyle(
                      color: MyColors.BordersGrey,
                      fontSize: 14,
                      fontFamily: "Roboto",
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTabletFormContent(double screenWidth, double screenHeight,
      GlobalController globalController) {
    return Column(
      children: [
        // Step Indicator
        Visibility(
          visible: controller.EmailSent.value == false,
          child: _buildStepIndicator(1, 2, globalController),
        ),
        Visibility(
          visible: controller.EmailSent.value == true,
          child: _buildStepIndicator(2, 2, globalController),
        ),

        SizedBox(height: 30),

        // Email Input (Step 1)
        Visibility(
          visible: controller.EmailSent.value == false,
          child: Myinput(
            labelText: globalController.lang.value == "fr"
                ? "Adresse email"
                : "Email address",
            controller: controller.email.value,
            icon: Icons.email_outlined,
            validate: (v) => v != null && !EmailValidator.validate(v)
                ? (globalController.lang.value == "fr"
                    ? 'Entrez un email valide'
                    : 'Enter a valid email')
                : null,
          ),
        ),

        // Code and Password Inputs (Step 2)
        Visibility(
          visible: controller.EmailSent.value == true,
          child: Column(
            children: [
              Myinput(
                labelText: globalController.lang.value == "fr"
                    ? "Code de validation"
                    : "Validation code",
                controller: controller.Codevalidation.value,
                validate: (v) => v == ""
                    ? (globalController.lang.value == "fr"
                        ? 'Code de validation requis'
                        : 'Validation code required')
                    : null,
                icon: Icons.security,
              ),
              SizedBox(height: 20),
              Myinput(
                labelText: globalController.lang.value == "fr"
                    ? "Nouveau mot de passe"
                    : "New password",
                controller: controller.password.value,
                validate: (v) => controller.validatePasswordlen(v!),
                obscureText: controller.passwtoggl.value,
                icon: Icons.lock_outline,
                Suffixicon: Icons.visibility_outlined,
                Suffixiconoff: Icons.visibility_off_outlined,
                suffixiconfun: () {
                  controller.passwtoggl.value = !controller.passwtoggl.value;
                },
              ),
              SizedBox(height: 20),
              Myinput(
                obscureText: controller.passwtogg2.value,
                labelText: globalController.lang.value == "fr"
                    ? "Confirmer le mot de passe"
                    : "Confirm password",
                controller: controller.confirmpaswword.value,
                validate: (v) => controller.validatePassword(
                    controller.password.value.text, v!),
                icon: Icons.lock_outline,
                Suffixicon: Icons.visibility_outlined,
                Suffixiconoff: Icons.visibility_off_outlined,
                suffixiconfun: () {
                  controller.passwtogg2.value = !controller.passwtogg2.value;
                },
              ),
            ],
          ),
        ),

        SizedBox(height: 25),

        // Error Message
        Visibility(
          visible: controller.showError.value,
          child: Container(
            padding: EdgeInsets.all(12),
            margin: EdgeInsets.only(bottom: 20),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.red.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                Icon(Icons.error_outline, color: Colors.red, size: 20),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    controller.errormsg.value,
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: 14,
                      fontFamily: "Roboto",
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        // Action Button
        controller.loading.value == false
            ? _buildActionButton(screenWidth, screenHeight, globalController)
            : Container(
                height: screenHeight * 0.065,
                child: Center(
                  child: CircularProgressIndicator(
                    color: MyColors.MainRedBig,
                    strokeWidth: 3,
                  ),
                ),
              ),

        // Resend Code Button
        Visibility(
          visible: controller.EmailSent.value == true,
          child: Padding(
            padding: EdgeInsets.only(top: 15),
            child: TextButton(
              onPressed: () async {
                if (controller.loading.value == false) {
                  await controller
                      .sendResetPassword(controller.email.value.text);
                }
              },
              style: TextButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              ),
              child: Text(
                globalController.lang.value == "fr"
                    ? "Renvoyer le code"
                    : "Resend code",
                style: TextStyle(
                  fontSize: 16,
                  fontFamily: "Roboto",
                  color: MyColors.thirdColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTabletFooter(GlobalController globalController) {
    return Column(
      children: [
        Row(
          children: <Widget>[
            Expanded(
              child: Divider(
                color: MyColors.Strokecolor.withOpacity(0.5),
                height: 1,
                thickness: 1,
                indent: 20,
                endIndent: 10,
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 15),
              child: Text(
                globalController.lang.value == "fr" ? "ou" : "or",
                style: TextStyle(
                  fontSize: 14,
                  color: MyColors.BordersGrey,
                  fontFamily: "Roboto",
                ),
              ),
            ),
            Expanded(
              child: Divider(
                color: MyColors.Strokecolor.withOpacity(0.5),
                height: 1,
                thickness: 1,
                indent: 10,
                endIndent: 20,
              ),
            ),
          ],
        ),
        SizedBox(height: 20),
        RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            style: TextStyle(
              color: MyColors.BordersGrey,
              fontSize: 15,
              fontFamily: "Roboto",
            ),
            children: [
              TextSpan(
                text: globalController.lang.value == "fr"
                    ? "Vous n'avez pas de compte? "
                    : "Don't have an account? ",
              ),
              TextSpan(
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    Get.offAll(LandingScreen());
                  },
                text: globalController.lang.value == "fr"
                    ? 'Inscrivez-vous'
                    : "Sign up",
                style: TextStyle(
                  color: MyColors.thirdColor,
                  fontWeight: FontWeight.w600,
                  decoration: TextDecoration.underline,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
